"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Eye,
  Edit,
  Trash2,
  Save,
  X,
  User,
  Mail,
  MessageSquare,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Users
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

// Types
interface Contact {
  _id: string;
  name: string;
  email: string;
  category: "general" | "technical" | "bug" | "feature" | "business";
  message: string;
  status: "new" | "read" | "in-progress" | "resolved";
  priority: "low" | "medium" | "high";
  assignedTo?: {
    _id: string;
    name: string;
    email: string;
  };
  internalNotes?: string;
  responseSent: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AdminUser {
  _id: string;
  name: string;
  email: string;
  role: "admin";
}

interface ContactDialogsProps {
  viewDialog: { open: boolean; contact: Contact | null };
  setViewDialog: (dialog: { open: boolean; contact: Contact | null }) => void;
  editDialog: { open: boolean; contact: Contact | null };
  setEditDialog: (dialog: { open: boolean; contact: Contact | null }) => void;
  replyDialog: { open: boolean; contact: Contact | null };
  setReplyDialog: (dialog: { open: boolean; contact: Contact | null }) => void;
  deleteDialog: { open: boolean; contactId: string | null };
  setDeleteDialog: (dialog: { open: boolean; contactId: string | null }) => void;
  bulkDialog: { open: boolean; action: string | null };
  setBulkDialog: (dialog: { open: boolean; action: string | null }) => void;
  selectedContacts: string[];
  onContactUpdate: () => void;
  onContactDelete: () => void;
  onBulkAction: () => void;
}

// Status badge colors
const getStatusBadge = (status: string) => {
  const variants = {
    new: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
    read: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400",
    "in-progress": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
    resolved: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
  };
  return variants[status as keyof typeof variants] || variants.new;
};

// Priority badge colors
const getPriorityBadge = (priority: string) => {
  const variants = {
    low: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400",
    medium: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
    high: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
  };
  return variants[priority as keyof typeof variants] || variants.medium;
};

// Category icons
const getCategoryIcon = (category: string) => {
  const icons = {
    general: <MessageSquare className="h-4 w-4" />,
    technical: <AlertCircle className="h-4 w-4" />,
    bug: <X className="h-4 w-4" />,
    feature: <CheckCircle className="h-4 w-4" />,
    business: <Users className="h-4 w-4" />,
  };
  return icons[category as keyof typeof icons] || icons.general;
};

export default function ContactDialogs({
  viewDialog,
  setViewDialog,
  editDialog,
  setEditDialog,
  replyDialog,
  setReplyDialog,
  deleteDialog,
  setDeleteDialog,
  bulkDialog,
  setBulkDialog,
  selectedContacts,
  onContactUpdate,
  onContactDelete,
  onBulkAction,
}: ContactDialogsProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [loadingAdminUsers, setLoadingAdminUsers] = useState(false);
  const [editForm, setEditForm] = useState({
    status: "",
    priority: "",
    assignedTo: "",
    internalNotes: "",
    responseSent: false,
  });

  const [replyForm, setReplyForm] = useState({
    subject: "",
    message: "",
    ccAdmin: false,
  });

  const [loadingReply, setLoadingReply] = useState(false);

  // Initialize edit form when dialog opens
  useEffect(() => {
    if (editDialog.open && editDialog.contact) {
      setEditForm({
        status: editDialog.contact.status,
        priority: editDialog.contact.priority,
        assignedTo: editDialog.contact.assignedTo?._id || "",
        internalNotes: editDialog.contact.internalNotes || "",
        responseSent: editDialog.contact.responseSent,
      });
    }
  }, [editDialog]);

  // Initialize reply form when dialog opens
  useEffect(() => {
    if (replyDialog.open && replyDialog.contact) {
      setReplyForm({
        subject: `Re: ${replyDialog.contact.category.charAt(0).toUpperCase() + replyDialog.contact.category.slice(1)} Inquiry`,
        message: `Dear ${replyDialog.contact.name},\n\nThank you for contacting us. `,
        ccAdmin: false,
      });
    }
  }, [replyDialog]);

  // Fetch admin users when edit dialog opens
  useEffect(() => {
    const fetchAdminUsers = async () => {
      if (!editDialog.open) return;

      try {
        setLoadingAdminUsers(true);
        const response = await fetch('/api/admin/users?role=admin&limit=100');

        if (!response.ok) {
          throw new Error('Failed to fetch admin users');
        }

        const data = await response.json();
        if (data.success) {
          setAdminUsers(data.data.users);
        } else {
          throw new Error(data.error || 'Failed to fetch admin users');
        }
      } catch (error: any) {
        console.error('Error fetching admin users:', error);
        toast({
          title: "Error",
          description: "Failed to load admin users for assignment",
          variant: "destructive",
        });
      } finally {
        setLoadingAdminUsers(false);
      }
    };

    fetchAdminUsers();
  }, [editDialog.open, toast]);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Handle contact update
  const handleUpdateContact = async () => {
    if (!editDialog.contact) return;

    try {
      setLoading(true);

      const response = await fetch(`/api/admin/contact/${editDialog.contact._id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        throw new Error("Failed to update contact");
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: "Contact updated successfully",
        });
        setEditDialog({ open: false, contact: null });
        onContactUpdate();
      } else {
        throw new Error(data.error || "Failed to update contact");
      }
    } catch (error: any) {
      console.error("Error updating contact:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to update contact",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle contact deletion
  const handleDeleteContact = async () => {
    if (!deleteDialog.contactId) return;

    try {
      setLoading(true);

      const response = await fetch(`/api/admin/contact/${deleteDialog.contactId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete contact");
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: "Contact deleted successfully",
        });
        setDeleteDialog({ open: false, contactId: null });
        onContactDelete();
      } else {
        throw new Error(data.error || "Failed to delete contact");
      }
    } catch (error: any) {
      console.error("Error deleting contact:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete contact",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk actions
  const handleBulkAction = async () => {
    if (!bulkDialog.action || selectedContacts.length === 0) return;

    try {
      setLoading(true);

      let actionData: any = {};

      switch (bulkDialog.action) {
        case "markRead":
          actionData = { action: "updateStatus", contactIds: selectedContacts, data: { status: "read" } };
          break;
        case "markResolved":
          actionData = { action: "updateStatus", contactIds: selectedContacts, data: { status: "resolved" } };
          break;
        case "delete":
          actionData = { action: "delete", contactIds: selectedContacts };
          break;
        default:
          throw new Error("Invalid action");
      }

      const response = await fetch("/api/admin/contact/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(actionData),
      });

      if (!response.ok) {
        throw new Error("Failed to perform bulk action");
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Bulk action completed successfully",
        });
        setBulkDialog({ open: false, action: null });
        onBulkAction();
      } else {
        throw new Error(data.error || "Failed to perform bulk action");
      }
    } catch (error: any) {
      console.error("Error performing bulk action:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to perform bulk action",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle reply submission
  const handleReplySubmit = async () => {
    if (!replyDialog.contact || !replyForm.subject.trim() || !replyForm.message.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoadingReply(true);

      const response = await fetch("/api/admin/contact/reply", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contactId: replyDialog.contact._id,
          subject: replyForm.subject,
          message: replyForm.message,
          ccAdmin: replyForm.ccAdmin,
          recipientEmail: replyDialog.contact.email,
          recipientName: replyDialog.contact.name,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send reply");
      }

      const result = await response.json();

      toast({
        title: "Success",
        description: "Reply sent successfully",
      });

      setReplyDialog({ open: false, contact: null });
      setReplyForm({
        subject: "",
        message: "",
        ccAdmin: false,
      });
      onContactUpdate(); // Refresh the contact list
    } catch (error) {
      console.error("Error sending reply:", error);
      toast({
        title: "Error",
        description: "Failed to send reply. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingReply(false);
    }
  };

  return (
    <>
      {/* View Contact Dialog */}
      <Dialog open={viewDialog.open} onOpenChange={(open) => setViewDialog({ open, contact: null })}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              Contact Details
            </DialogTitle>
          </DialogHeader>
          
          {viewDialog.contact && (
            <div className="space-y-6">
              {/* Contact Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Name</label>
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-900 dark:text-white">{viewDialog.contact.name}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Email</label>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-900 dark:text-white">{viewDialog.contact.email}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Category</label>
                  <div className="flex items-center">
                    {getCategoryIcon(viewDialog.contact.category)}
                    <span className="ml-2 capitalize text-gray-900 dark:text-white">
                      {viewDialog.contact.category}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Date Submitted</label>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-900 dark:text-white">
                      {formatDate(viewDialog.contact.createdAt)}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</label>
                  <Badge className={getStatusBadge(viewDialog.contact.status)}>
                    {viewDialog.contact.status === "in-progress" ? "In Progress" : viewDialog.contact.status}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Priority</label>
                  <Badge className={getPriorityBadge(viewDialog.contact.priority)}>
                    {viewDialog.contact.priority}
                  </Badge>
                </div>
              </div>

              {/* Message */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Message</label>
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-gray-900 dark:text-white whitespace-pre-wrap">
                    {viewDialog.contact.message}
                  </p>
                </div>
              </div>

              {/* Assigned To */}
              {viewDialog.contact.assignedTo ? (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Assigned To</label>
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-900 dark:text-white">
                      {viewDialog.contact.assignedTo.name} ({viewDialog.contact.assignedTo.email})
                    </span>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Assigned To</label>
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-500 dark:text-gray-500 italic">
                      Unassigned
                    </span>
                  </div>
                </div>
              )}

              {/* Internal Notes */}
              {viewDialog.contact.internalNotes && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Internal Notes</label>
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <p className="text-gray-900 dark:text-white whitespace-pre-wrap">
                      {viewDialog.contact.internalNotes}
                    </p>
                  </div>
                </div>
              )}

              {/* Response Status */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Response Status</label>
                <div className="flex items-center">
                  {viewDialog.contact.responseSent ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-green-600 dark:text-green-400">Response sent</span>
                    </>
                  ) : (
                    <>
                      <Clock className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="text-yellow-600 dark:text-yellow-400">No response sent</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Contact Dialog */}
      <Dialog open={editDialog.open} onOpenChange={(open) => setEditDialog({ open, contact: null })}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Edit className="h-5 w-5 mr-2" />
              Edit Contact
            </DialogTitle>
            <DialogDescription>
              Update contact status, priority, assignment, and internal notes.
            </DialogDescription>
          </DialogHeader>

          {editDialog.contact && (
            <div className="space-y-4">
              {/* Contact Info (Read-only) */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {editDialog.contact.name}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {editDialog.contact.email}
                  </span>
                </div>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  {getCategoryIcon(editDialog.contact.category)}
                  <span className="ml-2 capitalize">{editDialog.contact.category}</span>
                </div>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Status
                </label>
                <Select value={editForm.status} onValueChange={(value) => setEditForm(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="read">Read</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Priority
                </label>
                <Select value={editForm.priority} onValueChange={(value) => setEditForm(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Assigned To */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Assigned To
                </label>
                <Select
                  value={editForm.assignedTo}
                  onValueChange={(value) => setEditForm(prev => ({ ...prev, assignedTo: value }))}
                  disabled={loadingAdminUsers}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingAdminUsers ? "Loading admin users..." : "Select assignee"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Unassigned</SelectItem>
                    {adminUsers.map((user) => (
                      <SelectItem key={user._id} value={user._id}>
                        {user.name} ({user.email})
                      </SelectItem>
                    ))}
                    {adminUsers.length === 0 && !loadingAdminUsers && (
                      <SelectItem value="" disabled>
                        No admin users found
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Internal Notes */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Internal Notes
                </label>
                <Textarea
                  placeholder="Add internal notes (not visible to customer)..."
                  value={editForm.internalNotes}
                  onChange={(e) => setEditForm(prev => ({ ...prev, internalNotes: e.target.value }))}
                  rows={4}
                />
              </div>

              {/* Response Sent */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="responseSent"
                  checked={editForm.responseSent}
                  onChange={(e) => setEditForm(prev => ({ ...prev, responseSent: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="responseSent" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Response sent to customer
                </label>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditDialog({ open: false, contact: null })}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateContact} disabled={loading}>
              {loading ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="mr-2"
                  >
                    <Save className="h-4 w-4" />
                  </motion.div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Contact Dialog */}
      <Dialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({ open, contactId: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <Trash2 className="h-5 w-5 mr-2" />
              Delete Contact
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this contact? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ open: false, contactId: null })}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteContact}
              disabled={loading}
            >
              {loading ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="mr-2"
                  >
                    <Trash2 className="h-4 w-4" />
                  </motion.div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Contact
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Action Dialog */}
      <Dialog open={bulkDialog.open} onOpenChange={(open) => setBulkDialog({ open, action: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Bulk Action: {bulkDialog.action === "markRead" ? "Mark as Read" :
                           bulkDialog.action === "markResolved" ? "Mark as Resolved" :
                           bulkDialog.action === "delete" ? "Delete Contacts" : "Bulk Action"}
            </DialogTitle>
            <DialogDescription>
              {bulkDialog.action === "delete"
                ? `Are you sure you want to delete ${selectedContacts.length} contact${selectedContacts.length > 1 ? "s" : ""}? This action cannot be undone.`
                : `This will update ${selectedContacts.length} contact${selectedContacts.length > 1 ? "s" : ""}.`
              }
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setBulkDialog({ open: false, action: null })}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant={bulkDialog.action === "delete" ? "destructive" : "default"}
              onClick={handleBulkAction}
              disabled={loading}
            >
              {loading ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="mr-2"
                  >
                    <CheckCircle className="h-4 w-4" />
                  </motion.div>
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Confirm
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reply Dialog */}
      <Dialog open={replyDialog.open} onOpenChange={(open) => setReplyDialog({ open, contact: null })}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Reply to Contact
            </DialogTitle>
            <DialogDescription>
              Send a reply to {replyDialog.contact?.name} ({replyDialog.contact?.email})
            </DialogDescription>
          </DialogHeader>

          {replyDialog.contact && (
            <div className="space-y-4">
              {/* Original Message Context */}
              <div className="bg-muted/50 p-4 rounded-lg">
                <h4 className="font-medium text-sm text-muted-foreground mb-2">Original Message:</h4>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">From:</span>
                    <span>{replyDialog.contact.name} ({replyDialog.contact.email})</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Category:</span>
                    <Badge className={getPriorityBadge(replyDialog.contact.category)}>
                      {replyDialog.contact.category}
                    </Badge>
                  </div>
                  <div className="text-sm">
                    <span className="font-medium">Message:</span>
                    <p className="mt-1 text-muted-foreground">{replyDialog.contact.message}</p>
                  </div>
                </div>
              </div>

              {/* Reply Form */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Subject</label>
                  <Input
                    value={replyForm.subject}
                    onChange={(e) => setReplyForm({ ...replyForm, subject: e.target.value })}
                    placeholder="Reply subject"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Message</label>
                  <Textarea
                    value={replyForm.message}
                    onChange={(e) => setReplyForm({ ...replyForm, message: e.target.value })}
                    placeholder="Type your reply message..."
                    rows={8}
                    className="resize-none"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="ccAdmin"
                    checked={replyForm.ccAdmin}
                    onCheckedChange={(checked) =>
                      setReplyForm({ ...replyForm, ccAdmin: checked as boolean })
                    }
                  />
                  <label htmlFor="ccAdmin" className="text-sm">
                    CC admin team on this reply
                  </label>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setReplyDialog({ open: false, contact: null })}
              disabled={loadingReply}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReplySubmit}
              disabled={loadingReply || !replyForm.subject.trim() || !replyForm.message.trim()}
            >
              {loadingReply ? (
                <>
                  <motion.div
                    className="h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="h-4 w-4 mr-2" />
                  Send Reply
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
